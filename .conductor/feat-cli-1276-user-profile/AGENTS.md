# AGENTS.md

## Commands

- **Dev/Build**: `pnpm dev-client|dev-admin|dev-config`, `pnpm build-client|build-admin|build-config|build-all`
- **Quality**: `pnpm lint` | `pnpm lint:fix`, `pnpm type-check`, `pnpm format` | `pnpm format:check`

## Architecture

This is a Next.js 14 App Router monorepo with Nx workspace containing:

- **apps/client-portal**: Clinical research sites/investigators interface
- **apps/admin-portal**: Admin interface for studies/users/sites
- **apps/config-service**: Configuration management service
- **packages/shared-ui**: Shared components

Tech stack: TypeScript, Clerk auth, Zustand, React Query, Tailwind CSS, Flowbite React, React Hook Form + Zod

## Code Style

- Files ≤250 lines, kebab-case files/dirs, PascalCase components, camelCase hooks/APIs
- Imports: external libs → internal shared → app-specific → hooks → utils → types → assets
- Use `@/` absolute imports, prefer named exports, barrel exports for directories
- TypeScript strict mode, proper interfaces, avoid `any`, export shared types
- Components: hooks at top, event handlers, conditional renders, main render
- State: local → lift → context → Zustand (global) + React Query (server)
- Forms: React Hook Form + Zod validation with proper error handling
- No comments unless complex/non-trivial (per .cursor/rules)
